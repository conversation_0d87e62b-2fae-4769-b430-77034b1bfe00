
#!/usr/bin/env python3

from datetime import datetime
from langchain_ollama import OllamaLLM
from tools import search_tool, wiki_tool, save_tool

def research_agent():
    print("🤖 AI Research Agent with Llama 3.2")
    print("Available tools: Web Search, Wikipedia, Save to File")
    print("-" * 50)
    
    # Initialize the LLM
    llm = OllamaLLM(model="llama3.2")
    
    # Get user query
    query = input("What can I help you research? ")
    print(f"\n🔍 Researching: {query}")
    print("-" * 50)
    
    try:
        # Step 1: Search the web
        print("🌐 Searching the web for current information...")
        web_results = search_tool.run(query)
        print(f"   ✅ Web search completed. Found {len(web_results.split())} words of data.")

        # Step 2: Search Wikipedia
        print("📚 Searching Wikipedia for encyclopedic knowledge...")
        wiki_results = wiki_tool.run(query)
        print(f"   ✅ Wikipedia search completed. Found {len(wiki_results.split())} words of data.")
        
        # Step 3: Generate comprehensive analysis with multiple stages
        print("🧠 Analyzing and synthesizing information...")

        # Stage 1: Extract key insights and themes
        print("   📊 Extracting key insights...")
        analysis_prompt = f"""
        You are an expert research analyst. Analyze the following information about "{query}" and extract key insights:

        WEB SEARCH DATA:
        {web_results}

        WIKIPEDIA DATA:
        {wiki_results}

        Your task is to:
        1. Identify the main themes and concepts
        2. Extract the most important facts and statistics
        3. Note any trends, patterns, or relationships
        4. Identify different perspectives or viewpoints
        5. Highlight any recent developments or changes
        6. Note any controversies or debates

        Provide a detailed analytical breakdown focusing on insights rather than just summarizing the text.
        """

        insights = llm.invoke(analysis_prompt)

        # Stage 2: Generate comprehensive research report
        print("   📝 Generating comprehensive research report...")
        report_prompt = f"""
        You are a senior research analyst creating a comprehensive research report on "{query}".

        Based on your analysis:
        {insights}

        And the original data sources:
        - Web search results: {len(web_results)} characters of current information
        - Wikipedia results: {len(wiki_results)} characters of encyclopedic knowledge

        Create a detailed research report with the following structure:

        ## EXECUTIVE SUMMARY
        [2-3 sentences capturing the essence and significance of the topic]

        ## DETAILED ANALYSIS
        [Comprehensive analysis covering multiple aspects, not just repeating source text]

        ## KEY INSIGHTS & FINDINGS
        [Your analytical insights based on understanding the data, including:]
        - Critical observations
        - Important trends or patterns
        - Significant implications
        - Comparative analysis where relevant

        ## CURRENT STATE & RECENT DEVELOPMENTS
        [Latest information and recent changes in the field]

        ## DIFFERENT PERSPECTIVES
        [Various viewpoints, approaches, or schools of thought if applicable]

        ## IMPLICATIONS & SIGNIFICANCE
        [Why this topic matters, its impact, and broader implications]

        ## FUTURE OUTLOOK
        [Potential future developments, challenges, or opportunities]

        ## CONCLUSION
        [Synthesized conclusion based on your analysis]

        Focus on providing analytical insights and your understanding rather than just quoting sources.
        Make connections between different pieces of information and provide context.
        """

        summary = llm.invoke(report_prompt)
        
        # Step 4: Display results
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE RESEARCH ANALYSIS")
        print("="*80)
        print(summary)
        print("="*80)

        # Display data source summary
        print(f"\n📊 Research Data Sources:")
        print(f"   🌐 Web Search: {len(web_results.split())} words of current information")
        print(f"   📚 Wikipedia: {len(wiki_results.split())} words of encyclopedic knowledge")
        print(f"   🧠 AI Analysis: Multi-stage analytical processing with Llama 3.2")
        
        # Step 5: Ask if user wants to save
        save_choice = input("\n💾 Would you like to save this research to a file? (y/n): ").lower()
        if save_choice in ['y', 'yes']:
            filename = input("Enter filename (or press Enter for default): ").strip()
            if not filename:
                filename = f"research_{query.replace(' ', '_')[:20]}.txt"
            
            # Prepare enhanced content for saving
            save_content = f"""
================================================================================
AI RESEARCH ANALYSIS REPORT
================================================================================

RESEARCH TOPIC: {query}
RESEARCH DATE: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
ANALYSIS METHOD: Multi-stage AI analysis with Llama 3.2

================================================================================
COMPREHENSIVE RESEARCH ANALYSIS
================================================================================

{summary}

================================================================================
ANALYTICAL INSIGHTS EXTRACTED
================================================================================

{insights}

================================================================================
RAW DATA SOURCES
================================================================================

WEB SEARCH RESULTS ({len(web_results.split())} words):
{web_results}

WIKIPEDIA RESULTS ({len(wiki_results.split())} words):
{wiki_results}

================================================================================
RESEARCH METHODOLOGY
================================================================================

This report was generated using a multi-stage AI analysis process:
1. Data Collection: Web search and Wikipedia queries
2. Insight Extraction: AI analysis to identify key themes and patterns
3. Synthesis: Comprehensive report generation with analytical insights
4. Quality Enhancement: Focus on understanding rather than summarization

Generated by AI Research Agent with Llama 3.2 (Local AI Processing)
================================================================================
"""
            
            result = save_tool.run(f"data={save_content}&filename={filename}")
            print(f"✅ {result}")
        
        print("\n🎉 Advanced Research Analysis Completed Successfully!")
        print("   📈 Enhanced with multi-stage AI analysis")
        print("   🔍 Deep insights extracted from multiple sources")
        print("   🧠 Comprehensive understanding generated")
        
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        print("Please try again with a different query.")

if __name__ == "__main__":
    research_agent()
