#!/usr/bin/env python3

from datetime import datetime
from langchain_ollama import OllamaLLM
from tools import search_tool, wiki_tool, save_tool

def research_agent():
    print("🤖 AI Research Agent with Llama 3.2")
    print("Available tools: Web Search, Wikipedia, Save to File")
    print("-" * 50)
    
    # Initialize the LLM
    llm = OllamaLLM(model="llama3.2")
    
    # Get user query
    query = input("What can I help you research? ")
    print(f"\n🔍 Researching: {query}")
    print("-" * 50)
    
    try:
        # Step 1: Search the web
        print("🌐 Searching the web...")
        web_results = search_tool.run(query)
        print(f"Web search completed. Found information.")
        
        # Step 2: Search Wikipedia  
        print("📚 Searching Wikipedia...")
        wiki_results = wiki_tool.run(query)
        print(f"Wikipedia search completed.")
        
        # Step 3: Generate comprehensive research summary
        print("🧠 Generating research summary...")
        
        research_prompt = f"""
        Based on the following research query: "{query}"
        
        Web search results:
        {web_results}
        
        Wikipedia results:
        {wiki_results}
        
        Please provide a comprehensive research summary that includes:
        1. A clear overview of the topic
        2. Key findings from the research
        3. Important facts and details
        4. Conclusion
        
        Format your response as a well-structured research report.
        """
        
        summary = llm.invoke(research_prompt)
        
        # Step 4: Display results
        print("\n" + "="*60)
        print("📋 RESEARCH RESULTS")
        print("="*60)
        print(summary)
        print("="*60)
        
        # Step 5: Ask if user wants to save
        save_choice = input("\n💾 Would you like to save this research to a file? (y/n): ").lower()
        if save_choice in ['y', 'yes']:
            filename = input("Enter filename (or press Enter for default): ").strip()
            if not filename:
                filename = f"research_{query.replace(' ', '_')[:20]}.txt"
            
            # Prepare content for saving
            save_content = f"""
RESEARCH TOPIC: {query}
RESEARCH DATE: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

RESEARCH SUMMARY:
{summary}

WEB SEARCH RESULTS:
{web_results}

WIKIPEDIA RESULTS:
{wiki_results}
"""
            
            result = save_tool.run(f"data={save_content}&filename={filename}")
            print(f"✅ {result}")
        
        print("\n🎉 Research completed successfully!")
        
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        print("Please try again with a different query.")

if __name__ == "__main__":
    research_agent()
