Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   AI Research Agent - Web Interface" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Starting the web interface..." -ForegroundColor Green
Write-Host "📱 The web interface will open at: http://localhost:5000" -ForegroundColor Yellow
Write-Host "🤖 Powered by Llama 3.2 via Ollama" -ForegroundColor Blue
Write-Host ""

# Activate virtual environment and start the web server
.\RAgent_venv\Scripts\Activate.ps1
.\RAgent_venv\Scripts\python.exe app.py

Read-Host "Press Enter to exit"
