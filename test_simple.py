#!/usr/bin/env python3

print("🔍 Testing simple imports...")

try:
    print("Testing basic imports...")
    from tools import search_tool, wiki_tool, save_tool
    print("✅ Tools imported successfully")
    
    print("Testing Ollama...")
    from langchain_ollama import OllamaLLM
    print("✅ OllamaLLM imported successfully")
    
    print("Initializing Ollama...")
    llm = OllamaLLM(model="llama3.2")
    print("✅ Ollama initialized successfully")
    
    print("Testing a simple query...")
    response = llm.invoke("What is artificial intelligence in one sentence?")
    print(f"✅ Response: {response}")
    
    print("\n🎉 All tests passed! Your setup is working correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
