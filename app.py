#!/usr/bin/env python3

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from datetime import datetime
import os
import json
import uuid
import threading
from langchain_ollama import OllamaLLM
from tools import search_tool, wiki_tool, save_tool

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize the LLM
llm = OllamaLLM(model="llama3.2")

class ResearchAgent:
    def __init__(self):
        self.llm = llm

    def conduct_research(self, query, session_id=None):
        """Conduct comprehensive research with real-time progress updates"""
        def emit_progress(message, step=None, progress=None):
            if session_id:
                socketio.emit('progress_update', {
                    'message': message,
                    'step': step,
                    'progress': progress,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                }, room=session_id)

        results = {
            'query': query,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'starting',
            'web_results': '',
            'wiki_results': '',
            'insights': '',
            'final_report': '',
            'error': None
        }

        try:
            # Step 1: Web Search
            emit_progress("🌐 Searching the web for current information...", "web_search", 10)
            results['status'] = 'web_search'
            results['web_results'] = search_tool.run(query)
            emit_progress(f"   ✅ Web search completed. Found {len(results['web_results'].split())} words of data.", "web_search", 25)

            # Step 2: Wikipedia Search
            emit_progress("📚 Searching Wikipedia for encyclopedic knowledge...", "wiki_search", 30)
            results['status'] = 'wiki_search'
            results['wiki_results'] = wiki_tool.run(query)
            emit_progress(f"   ✅ Wikipedia search completed. Found {len(results['wiki_results'].split())} words of data.", "wiki_search", 45)
            
            # Step 3: Extract Insights
            emit_progress("📊 Extracting key insights and analyzing data...", "analyzing", 50)
            results['status'] = 'analyzing'

            analysis_prompt = f"""
            You are an expert research analyst. Analyze the following information about "{query}" and extract key insights:

            WEB SEARCH DATA:
            {results['web_results']}

            WIKIPEDIA DATA:
            {results['wiki_results']}

            Your task is to:
            1. Identify the main themes and concepts
            2. Extract the most important facts and statistics
            3. Note any trends, patterns, or relationships
            4. Identify different perspectives or viewpoints
            5. Highlight any recent developments or changes
            6. Note any controversies or debates

            Provide a detailed analytical breakdown focusing on insights rather than just summarizing the text.
            """

            emit_progress("   🧠 AI is analyzing the collected data...", "analyzing", 60)
            results['insights'] = self.llm.invoke(analysis_prompt)
            emit_progress("   ✅ Key insights extracted successfully.", "analyzing", 70)

            # Step 4: Generate Final Report
            emit_progress("📝 Generating comprehensive research report...", "generating_report", 75)
            results['status'] = 'generating_report'
            
            report_prompt = f"""
            You are a senior research analyst creating a comprehensive research report on "{query}".

            Based on your analysis:
            {results['insights']}

            And the original data sources:
            - Web search results: {len(results['web_results'])} characters of current information
            - Wikipedia results: {len(results['wiki_results'])} characters of encyclopedic knowledge

            Create a detailed research report with the following structure:

            ## EXECUTIVE SUMMARY
            [2-3 sentences capturing the essence and significance of the topic]

            ## DETAILED ANALYSIS
            [Comprehensive analysis covering multiple aspects, not just repeating source text]

            ## KEY INSIGHTS & FINDINGS
            [Your analytical insights based on understanding the data, including:]
            - Critical observations
            - Important trends or patterns
            - Significant implications
            - Comparative analysis where relevant

            ## CURRENT STATE & RECENT DEVELOPMENTS
            [Latest information and recent changes in the field]

            ## DIFFERENT PERSPECTIVES
            [Various viewpoints, approaches, or schools of thought if applicable]

            ## IMPLICATIONS & SIGNIFICANCE
            [Why this topic matters, its impact, and broader implications]

            ## FUTURE OUTLOOK
            [Potential future developments, challenges, or opportunities]

            ## CONCLUSION
            [Synthesized conclusion based on your analysis]

            Focus on providing analytical insights and your understanding rather than just quoting sources.
            Make connections between different pieces of information and provide context.
            """

            emit_progress("   🧠 AI is generating comprehensive report...", "generating_report", 85)
            results['final_report'] = self.llm.invoke(report_prompt)
            emit_progress("   ✅ Research report generated successfully.", "generating_report", 95)

            results['status'] = 'completed'
            emit_progress("🎉 Research analysis completed successfully!", "completed", 100)

        except Exception as e:
            results['status'] = 'error'
            results['error'] = str(e)
            emit_progress(f"❌ Error: {str(e)}", "error", 0)

        return results

# Initialize research agent
research_agent = ResearchAgent()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print(f"Client connected: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print(f"Client disconnected: {request.sid}")

@socketio.on('start_research')
def handle_research(data):
    """Handle research request via WebSocket"""
    query = data.get('query', '').strip()

    if not query:
        emit('error', {'message': 'Query is required'})
        return

    # Generate unique research ID
    research_id = str(uuid.uuid4())
    session_id = request.sid

    def run_research():
        """Run research in background thread"""
        try:
            # Emit initial status
            socketio.emit('research_started', {
                'research_id': research_id,
                'query': query,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }, room=session_id)

            # Conduct research with real-time updates
            results = research_agent.conduct_research(query, session_id)

            # Store results in session
            session[research_id] = results

            # Emit completion
            socketio.emit('research_completed', {
                'research_id': research_id,
                'results': results
            }, room=session_id)

        except Exception as e:
            socketio.emit('research_error', {
                'research_id': research_id,
                'error': str(e)
            }, room=session_id)

    # Start research in background thread
    thread = threading.Thread(target=run_research)
    thread.daemon = True
    thread.start()

@app.route('/api/research', methods=['POST'])
def api_research():
    """API endpoint for conducting research (fallback for non-WebSocket clients)"""
    data = request.get_json()
    query = data.get('query', '').strip()

    if not query:
        return jsonify({'error': 'Query is required'}), 400

    # Generate unique session ID for this research
    research_id = str(uuid.uuid4())
    session[research_id] = {'status': 'starting'}

    try:
        results = research_agent.conduct_research(query)
        session[research_id] = results
        return jsonify({
            'research_id': research_id,
            'results': results
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/save', methods=['POST'])
def api_save():
    """API endpoint for saving research results"""
    data = request.get_json()
    research_id = data.get('research_id')
    filename = data.get('filename', '').strip()
    
    if not research_id or research_id not in session:
        return jsonify({'error': 'Invalid research ID'}), 400
    
    results = session[research_id]
    
    if not filename:
        filename = f"research_{results['query'].replace(' ', '_')[:20]}.txt"
    
    # Prepare content for saving
    save_content = f"""
================================================================================
AI RESEARCH ANALYSIS REPORT
================================================================================

RESEARCH TOPIC: {results['query']}
RESEARCH DATE: {results['timestamp']}
ANALYSIS METHOD: Multi-stage AI analysis with Llama 3.2

================================================================================
COMPREHENSIVE RESEARCH ANALYSIS
================================================================================

{results['final_report']}

================================================================================
ANALYTICAL INSIGHTS EXTRACTED
================================================================================

{results['insights']}

================================================================================
RAW DATA SOURCES
================================================================================

WEB SEARCH RESULTS ({len(results['web_results'].split())} words):
{results['web_results']}

WIKIPEDIA RESULTS ({len(results['wiki_results'].split())} words):
{results['wiki_results']}

================================================================================
RESEARCH METHODOLOGY
================================================================================

This report was generated using a multi-stage AI analysis process:
1. Data Collection: Web search and Wikipedia queries
2. Insight Extraction: AI analysis to identify key themes and patterns
3. Synthesis: Comprehensive report generation with analytical insights
4. Quality Enhancement: Focus on understanding rather than summarization

Generated by AI Research Agent with Llama 3.2 (Local AI Processing)
Created by Chaitanya with Augment under Open Network Solutions
================================================================================
"""
    
    try:
        result = save_tool.run(f"data={save_content}&filename={filename}")
        return jsonify({'message': result, 'filename': filename})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')
    if not os.path.exists('static'):
        os.makedirs('static')

    print("🚀 Starting AI Research Agent Web Interface...")
    print("📱 Access the web interface at: http://localhost:5000")
    print("🤖 Powered by Llama 3.2 via Ollama")
    print("⚡ Real-time progress updates enabled")
    print("-" * 50)

    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
