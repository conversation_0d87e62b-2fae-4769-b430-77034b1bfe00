#!/usr/bin/env python3

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from datetime import datetime
import os
import uuid
import threading

from langchain_ollama import OllamaLLM
from tools import search_tool, wiki_tool

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Global storage for research results (in production, use a database)
research_results = {}

# Initialize the LLM
llm = OllamaLLM(model="llama3.2")

class ResearchAgent:
    def __init__(self):
        self.llm = llm

    def conduct_research(self, query, research_id, session_id=None):
        """Conduct comprehensive research with real-time progress updates"""
        def emit_progress(message, step=None, progress=None):
            if session_id:
                socketio.emit('progress_update', {
                    'message': message,
                    'step': step,
                    'progress': progress,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                }, room=session_id)

        results = {
            'research_id': research_id,
            'query': query,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'starting',
            'web_results': '',
            'wiki_results': '',
            'insights': '',
            'final_report': '',
            'error': None,
            'word_counts': {
                'web': 0,
                'wiki': 0,
                'total': 0
            },
            'analysis_time': 0
        }

        start_time = datetime.now()

        try:
            # Store initial results
            research_results[research_id] = results

            # Step 1: Web Search
            emit_progress("🌐 Searching the web for current information...", "web_search", 10)
            results['status'] = 'web_search'
            results['web_results'] = search_tool.run(query)
            results['word_counts']['web'] = len(results['web_results'].split())
            emit_progress(f"   ✅ Web search completed. Found {results['word_counts']['web']} words of data.", "web_search", 25)

            # Step 2: Wikipedia Search
            emit_progress("📚 Searching Wikipedia for encyclopedic knowledge...", "wiki_search", 30)
            results['status'] = 'wiki_search'
            results['wiki_results'] = wiki_tool.run(query)
            results['word_counts']['wiki'] = len(results['wiki_results'].split())
            results['word_counts']['total'] = results['word_counts']['web'] + results['word_counts']['wiki']
            emit_progress(f"   ✅ Wikipedia search completed. Found {results['word_counts']['wiki']} words of data.", "wiki_search", 45)
            
            # Step 3: Extract Insights
            emit_progress("📊 Extracting key insights and analyzing data...", "analyzing", 50)
            results['status'] = 'analyzing'

            analysis_prompt = f"""
            You are an expert research analyst. Analyze the following information about "{query}" and extract key insights:

            WEB SEARCH DATA:
            {results['web_results']}

            WIKIPEDIA DATA:
            {results['wiki_results']}

            Your task is to:
            1. Identify the main themes and concepts
            2. Extract the most important facts and statistics
            3. Note any trends, patterns, or relationships
            4. Identify different perspectives or viewpoints
            5. Highlight any recent developments or changes
            6. Note any controversies or debates

            Provide a detailed analytical breakdown focusing on insights rather than just summarizing the text.
            """

            emit_progress("   🧠 AI is analyzing the collected data...", "analyzing", 60)
            results['insights'] = self.llm.invoke(analysis_prompt)
            emit_progress("   ✅ Key insights extracted successfully.", "analyzing", 70)

            # Step 4: Generate Final Report
            emit_progress("📝 Generating comprehensive research report...", "generating_report", 75)
            results['status'] = 'generating_report'
            
            report_prompt = f"""
            You are a senior research analyst creating a comprehensive research report on "{query}".

            Based on your analysis:
            {results['insights']}

            And the original data sources:
            - Web search results: {len(results['web_results'])} characters of current information
            - Wikipedia results: {len(results['wiki_results'])} characters of encyclopedic knowledge

            Create a detailed research report with the following structure:

            ## EXECUTIVE SUMMARY
            [2-3 sentences capturing the essence and significance of the topic]

            ## DETAILED ANALYSIS
            [Comprehensive analysis covering multiple aspects, not just repeating source text]

            ## KEY INSIGHTS & FINDINGS
            [Your analytical insights based on understanding the data, including:]
            - Critical observations
            - Important trends or patterns
            - Significant implications
            - Comparative analysis where relevant

            ## CURRENT STATE & RECENT DEVELOPMENTS
            [Latest information and recent changes in the field]

            ## DIFFERENT PERSPECTIVES
            [Various viewpoints, approaches, or schools of thought if applicable]

            ## IMPLICATIONS & SIGNIFICANCE
            [Why this topic matters, its impact, and broader implications]

            ## FUTURE OUTLOOK
            [Potential future developments, challenges, or opportunities]

            ## CONCLUSION
            [Synthesized conclusion based on your analysis]

            Focus on providing analytical insights and your understanding rather than just quoting sources.
            Make connections between different pieces of information and provide context.
            """

            emit_progress("   🧠 AI is generating comprehensive report...", "generating_report", 85)
            results['final_report'] = self.llm.invoke(report_prompt)
            emit_progress("   ✅ Research report generated successfully.", "generating_report", 95)

            # Calculate analysis time
            end_time = datetime.now()
            results['analysis_time'] = int((end_time - start_time).total_seconds())

            results['status'] = 'completed'

            # Store final results
            research_results[research_id] = results

            emit_progress("🎉 Research analysis completed successfully!", "completed", 100)

        except Exception as e:
            results['status'] = 'error'
            results['error'] = str(e)
            research_results[research_id] = results
            emit_progress(f"❌ Error: {str(e)}", "error", 0)

        return results

# Initialize research agent
research_agent = ResearchAgent()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print(f"Client connected: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print(f"Client disconnected: {request.sid}")

@socketio.on('start_research')
def handle_research(data):
    """Handle research request via WebSocket"""
    query = data.get('query', '').strip()

    if not query:
        emit('error', {'message': 'Query is required'})
        return

    # Generate unique research ID
    research_id = str(uuid.uuid4())
    session_id = request.sid

    def run_research():
        """Run research in background thread"""
        try:
            # Emit initial status
            socketio.emit('research_started', {
                'research_id': research_id,
                'query': query,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }, room=session_id)

            # Conduct research with real-time updates
            results = research_agent.conduct_research(query, research_id, session_id)

            # Emit completion
            socketio.emit('research_completed', {
                'research_id': research_id,
                'results': results
            }, room=session_id)

        except Exception as e:
            socketio.emit('research_error', {
                'research_id': research_id,
                'error': str(e)
            }, room=session_id)

    # Start research in background thread
    thread = threading.Thread(target=run_research)
    thread.daemon = True
    thread.start()

@app.route('/api/research/<research_id>')
def get_research_results(research_id):
    """Get research results by ID"""
    if research_id not in research_results:
        return jsonify({'error': 'Research not found'}), 404

    return jsonify(research_results[research_id])

@app.route('/api/download/<research_id>')
def download_research_report(research_id):
    """Download research report as text file"""
    if research_id not in research_results:
        return jsonify({'error': 'Research not found'}), 404

    results = research_results[research_id]

    if results['status'] != 'completed':
        return jsonify({'error': 'Research not completed yet'}), 400

    # Create report content
    report_content = f"""AI RESEARCH REPORT
{'=' * 50}

Query: {results['query']}
Generated: {results['timestamp']}
Analysis Time: {results['analysis_time']} seconds

STATISTICS:
- Web Search Results: {results['word_counts']['web']} words
- Wikipedia Results: {results['word_counts']['wiki']} words
- Total Data Processed: {results['word_counts']['total']} words

{'=' * 50}
COMPREHENSIVE RESEARCH REPORT
{'=' * 50}

{results['final_report']}

{'=' * 50}
Created by Chaitanya with Augment under Open Network Solutions
Privacy-focused • Local AI Processing • Completely Free
"""

    # Create a temporary file
    import tempfile

    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8')
    temp_file.write(report_content)
    temp_file.close()

    # Generate filename
    safe_query = "".join(c for c in results['query'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_query = safe_query.replace(' ', '_')[:50]  # Limit length
    filename = f"research_report_{safe_query}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    return send_file(
        temp_file.name,
        as_attachment=True,
        download_name=filename,
        mimetype='text/plain'
    )





if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')
    if not os.path.exists('static'):
        os.makedirs('static')

    print("🚀 Starting AI Research Agent Web Interface...")
    print("📱 Access the web interface at: http://localhost:5000")
    print("🤖 Powered by Llama 3.2 via Ollama")
    print("⚡ Real-time progress updates enabled")
    print("-" * 50)

    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
