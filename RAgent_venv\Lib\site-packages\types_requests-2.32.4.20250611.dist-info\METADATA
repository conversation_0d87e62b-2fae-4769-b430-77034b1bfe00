Metadata-Version: 2.4
Name: types-requests
Version: 2.32.4.20250611
Summary: Typing stubs for requests
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/requests.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: urllib3>=2
Dynamic: license-file

## Typing stubs for requests

This is a [PEP 561](https://peps.python.org/pep-0561/)
type stub package for the [`requests`](https://github.com/psf/requests) package.
It can be used by type-checking tools like
[mypy](https://github.com/python/mypy/),
[pyright](https://github.com/microsoft/pyright),
[pytype](https://github.com/google/pytype/),
[Pyre](https://pyre-check.org/),
PyCharm, etc. to check code that uses `requests`. This version of
`types-requests` aims to provide accurate annotations for
`requests~=2.32.4`.

Note: `types-requests` has required `urllib3>=2` since v********. If you need to install `types-requests` into an environment that must also have `urllib3<2` installed into it, you will have to use `types-requests<********`.

This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/requests`](https://github.com/python/typeshed/tree/main/stubs/requests)
directory.

This package was tested with
mypy 1.16.0,
pyright 1.1.400,
and pytype 2024.10.11.
It was generated from typeshed commit
[`0db0486fe6a0eb6b4ebf1256fe0afe0b95e2dc36`](https://github.com/python/typeshed/commit/0db0486fe6a0eb6b4ebf1256fe0afe0b95e2dc36).
