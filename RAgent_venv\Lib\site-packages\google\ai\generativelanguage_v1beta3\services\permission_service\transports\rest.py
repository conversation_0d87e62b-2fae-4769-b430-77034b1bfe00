# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
import dataclasses
import json  # type: ignore
import logging
from typing import Any, Callable, Dict, List, Optional, Sequence, Tuple, Union
import warnings

from google.api_core import exceptions as core_exceptions
from google.api_core import gapic_v1, rest_helpers, rest_streaming
from google.api_core import retry as retries
from google.auth import credentials as ga_credentials  # type: ignore
from google.auth.transport.requests import AuthorizedSession  # type: ignore
from google.longrunning import operations_pb2  # type: ignore
from google.protobuf import empty_pb2  # type: ignore
from google.protobuf import json_format
from requests import __version__ as requests_version

from google.ai.generativelanguage_v1beta3.types import permission as gag_permission
from google.ai.generativelanguage_v1beta3.types import permission
from google.ai.generativelanguage_v1beta3.types import permission_service

from .base import DEFAULT_CLIENT_INFO as BASE_DEFAULT_CLIENT_INFO
from .rest_base import _BasePermissionServiceRestTransport

try:
    OptionalRetry = Union[retries.Retry, gapic_v1.method._MethodDefault, None]
except AttributeError:  # pragma: NO COVER
    OptionalRetry = Union[retries.Retry, object, None]  # type: ignore

try:
    from google.api_core import client_logging  # type: ignore

    CLIENT_LOGGING_SUPPORTED = True  # pragma: NO COVER
except ImportError:  # pragma: NO COVER
    CLIENT_LOGGING_SUPPORTED = False

_LOGGER = logging.getLogger(__name__)

DEFAULT_CLIENT_INFO = gapic_v1.client_info.ClientInfo(
    gapic_version=BASE_DEFAULT_CLIENT_INFO.gapic_version,
    grpc_version=None,
    rest_version=f"requests@{requests_version}",
)


class PermissionServiceRestInterceptor:
    """Interceptor for PermissionService.

    Interceptors are used to manipulate requests, request metadata, and responses
    in arbitrary ways.
    Example use cases include:
    * Logging
    * Verifying requests according to service or custom semantics
    * Stripping extraneous information from responses

    These use cases and more can be enabled by injecting an
    instance of a custom subclass when constructing the PermissionServiceRestTransport.

    .. code-block:: python
        class MyCustomPermissionServiceInterceptor(PermissionServiceRestInterceptor):
            def pre_create_permission(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def post_create_permission(self, response):
                logging.log(f"Received response: {response}")
                return response

            def pre_delete_permission(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def pre_get_permission(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def post_get_permission(self, response):
                logging.log(f"Received response: {response}")
                return response

            def pre_list_permissions(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def post_list_permissions(self, response):
                logging.log(f"Received response: {response}")
                return response

            def pre_transfer_ownership(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def post_transfer_ownership(self, response):
                logging.log(f"Received response: {response}")
                return response

            def pre_update_permission(self, request, metadata):
                logging.log(f"Received request: {request}")
                return request, metadata

            def post_update_permission(self, response):
                logging.log(f"Received response: {response}")
                return response

        transport = PermissionServiceRestTransport(interceptor=MyCustomPermissionServiceInterceptor())
        client = PermissionServiceClient(transport=transport)


    """

    def pre_create_permission(
        self,
        request: permission_service.CreatePermissionRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.CreatePermissionRequest,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Pre-rpc interceptor for create_permission

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def post_create_permission(
        self, response: gag_permission.Permission
    ) -> gag_permission.Permission:
        """Post-rpc interceptor for create_permission

        DEPRECATED. Please use the `post_create_permission_with_metadata`
        interceptor instead.

        Override in a subclass to read or manipulate the response
        after it is returned by the PermissionService server but before
        it is returned to user code. This `post_create_permission` interceptor runs
        before the `post_create_permission_with_metadata` interceptor.
        """
        return response

    def post_create_permission_with_metadata(
        self,
        response: gag_permission.Permission,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[gag_permission.Permission, Sequence[Tuple[str, Union[str, bytes]]]]:
        """Post-rpc interceptor for create_permission

        Override in a subclass to read or manipulate the response or metadata after it
        is returned by the PermissionService server but before it is returned to user code.

        We recommend only using this `post_create_permission_with_metadata`
        interceptor in new development instead of the `post_create_permission` interceptor.
        When both interceptors are used, this `post_create_permission_with_metadata` interceptor runs after the
        `post_create_permission` interceptor. The (possibly modified) response returned by
        `post_create_permission` will be passed to
        `post_create_permission_with_metadata`.
        """
        return response, metadata

    def pre_delete_permission(
        self,
        request: permission_service.DeletePermissionRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.DeletePermissionRequest,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Pre-rpc interceptor for delete_permission

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def pre_get_permission(
        self,
        request: permission_service.GetPermissionRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.GetPermissionRequest, Sequence[Tuple[str, Union[str, bytes]]]
    ]:
        """Pre-rpc interceptor for get_permission

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def post_get_permission(
        self, response: permission.Permission
    ) -> permission.Permission:
        """Post-rpc interceptor for get_permission

        DEPRECATED. Please use the `post_get_permission_with_metadata`
        interceptor instead.

        Override in a subclass to read or manipulate the response
        after it is returned by the PermissionService server but before
        it is returned to user code. This `post_get_permission` interceptor runs
        before the `post_get_permission_with_metadata` interceptor.
        """
        return response

    def post_get_permission_with_metadata(
        self,
        response: permission.Permission,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[permission.Permission, Sequence[Tuple[str, Union[str, bytes]]]]:
        """Post-rpc interceptor for get_permission

        Override in a subclass to read or manipulate the response or metadata after it
        is returned by the PermissionService server but before it is returned to user code.

        We recommend only using this `post_get_permission_with_metadata`
        interceptor in new development instead of the `post_get_permission` interceptor.
        When both interceptors are used, this `post_get_permission_with_metadata` interceptor runs after the
        `post_get_permission` interceptor. The (possibly modified) response returned by
        `post_get_permission` will be passed to
        `post_get_permission_with_metadata`.
        """
        return response, metadata

    def pre_list_permissions(
        self,
        request: permission_service.ListPermissionsRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.ListPermissionsRequest,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Pre-rpc interceptor for list_permissions

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def post_list_permissions(
        self, response: permission_service.ListPermissionsResponse
    ) -> permission_service.ListPermissionsResponse:
        """Post-rpc interceptor for list_permissions

        DEPRECATED. Please use the `post_list_permissions_with_metadata`
        interceptor instead.

        Override in a subclass to read or manipulate the response
        after it is returned by the PermissionService server but before
        it is returned to user code. This `post_list_permissions` interceptor runs
        before the `post_list_permissions_with_metadata` interceptor.
        """
        return response

    def post_list_permissions_with_metadata(
        self,
        response: permission_service.ListPermissionsResponse,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.ListPermissionsResponse,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Post-rpc interceptor for list_permissions

        Override in a subclass to read or manipulate the response or metadata after it
        is returned by the PermissionService server but before it is returned to user code.

        We recommend only using this `post_list_permissions_with_metadata`
        interceptor in new development instead of the `post_list_permissions` interceptor.
        When both interceptors are used, this `post_list_permissions_with_metadata` interceptor runs after the
        `post_list_permissions` interceptor. The (possibly modified) response returned by
        `post_list_permissions` will be passed to
        `post_list_permissions_with_metadata`.
        """
        return response, metadata

    def pre_transfer_ownership(
        self,
        request: permission_service.TransferOwnershipRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.TransferOwnershipRequest,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Pre-rpc interceptor for transfer_ownership

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def post_transfer_ownership(
        self, response: permission_service.TransferOwnershipResponse
    ) -> permission_service.TransferOwnershipResponse:
        """Post-rpc interceptor for transfer_ownership

        DEPRECATED. Please use the `post_transfer_ownership_with_metadata`
        interceptor instead.

        Override in a subclass to read or manipulate the response
        after it is returned by the PermissionService server but before
        it is returned to user code. This `post_transfer_ownership` interceptor runs
        before the `post_transfer_ownership_with_metadata` interceptor.
        """
        return response

    def post_transfer_ownership_with_metadata(
        self,
        response: permission_service.TransferOwnershipResponse,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.TransferOwnershipResponse,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Post-rpc interceptor for transfer_ownership

        Override in a subclass to read or manipulate the response or metadata after it
        is returned by the PermissionService server but before it is returned to user code.

        We recommend only using this `post_transfer_ownership_with_metadata`
        interceptor in new development instead of the `post_transfer_ownership` interceptor.
        When both interceptors are used, this `post_transfer_ownership_with_metadata` interceptor runs after the
        `post_transfer_ownership` interceptor. The (possibly modified) response returned by
        `post_transfer_ownership` will be passed to
        `post_transfer_ownership_with_metadata`.
        """
        return response, metadata

    def pre_update_permission(
        self,
        request: permission_service.UpdatePermissionRequest,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[
        permission_service.UpdatePermissionRequest,
        Sequence[Tuple[str, Union[str, bytes]]],
    ]:
        """Pre-rpc interceptor for update_permission

        Override in a subclass to manipulate the request or metadata
        before they are sent to the PermissionService server.
        """
        return request, metadata

    def post_update_permission(
        self, response: gag_permission.Permission
    ) -> gag_permission.Permission:
        """Post-rpc interceptor for update_permission

        DEPRECATED. Please use the `post_update_permission_with_metadata`
        interceptor instead.

        Override in a subclass to read or manipulate the response
        after it is returned by the PermissionService server but before
        it is returned to user code. This `post_update_permission` interceptor runs
        before the `post_update_permission_with_metadata` interceptor.
        """
        return response

    def post_update_permission_with_metadata(
        self,
        response: gag_permission.Permission,
        metadata: Sequence[Tuple[str, Union[str, bytes]]],
    ) -> Tuple[gag_permission.Permission, Sequence[Tuple[str, Union[str, bytes]]]]:
        """Post-rpc interceptor for update_permission

        Override in a subclass to read or manipulate the response or metadata after it
        is returned by the PermissionService server but before it is returned to user code.

        We recommend only using this `post_update_permission_with_metadata`
        interceptor in new development instead of the `post_update_permission` interceptor.
        When both interceptors are used, this `post_update_permission_with_metadata` interceptor runs after the
        `post_update_permission` interceptor. The (possibly modified) response returned by
        `post_update_permission` will be passed to
        `post_update_permission_with_metadata`.
        """
        return response, metadata


@dataclasses.dataclass
class PermissionServiceRestStub:
    _session: AuthorizedSession
    _host: str
    _interceptor: PermissionServiceRestInterceptor


class PermissionServiceRestTransport(_BasePermissionServiceRestTransport):
    """REST backend synchronous transport for PermissionService.

    Provides methods for managing permissions to PaLM API
    resources.

    This class defines the same methods as the primary client, so the
    primary client can load the underlying transport implementation
    and call it.

    It sends JSON representations of protocol buffers over HTTP/1.1
    """

    def __init__(
        self,
        *,
        host: str = "generativelanguage.googleapis.com",
        credentials: Optional[ga_credentials.Credentials] = None,
        credentials_file: Optional[str] = None,
        scopes: Optional[Sequence[str]] = None,
        client_cert_source_for_mtls: Optional[Callable[[], Tuple[bytes, bytes]]] = None,
        quota_project_id: Optional[str] = None,
        client_info: gapic_v1.client_info.ClientInfo = DEFAULT_CLIENT_INFO,
        always_use_jwt_access: Optional[bool] = False,
        url_scheme: str = "https",
        interceptor: Optional[PermissionServiceRestInterceptor] = None,
        api_audience: Optional[str] = None,
    ) -> None:
        """Instantiate the transport.

        Args:
            host (Optional[str]):
                 The hostname to connect to (default: 'generativelanguage.googleapis.com').
            credentials (Optional[google.auth.credentials.Credentials]): The
                authorization credentials to attach to requests. These
                credentials identify the application to the service; if none
                are specified, the client will attempt to ascertain the
                credentials from the environment.

            credentials_file (Optional[str]): A file with credentials that can
                be loaded with :func:`google.auth.load_credentials_from_file`.
                This argument is ignored if ``channel`` is provided.
            scopes (Optional(Sequence[str])): A list of scopes. This argument is
                ignored if ``channel`` is provided.
            client_cert_source_for_mtls (Callable[[], Tuple[bytes, bytes]]): Client
                certificate to configure mutual TLS HTTP channel. It is ignored
                if ``channel`` is provided.
            quota_project_id (Optional[str]): An optional project to use for billing
                and quota.
            client_info (google.api_core.gapic_v1.client_info.ClientInfo):
                The client info used to send a user-agent string along with
                API requests. If ``None``, then default info will be used.
                Generally, you only need to set this if you are developing
                your own client library.
            always_use_jwt_access (Optional[bool]): Whether self signed JWT should
                be used for service account credentials.
            url_scheme: the protocol scheme for the API endpoint.  Normally
                "https", but for testing or local servers,
                "http" can be specified.
        """
        # Run the base constructor
        # TODO(yon-mg): resolve other ctor params i.e. scopes, quota, etc.
        # TODO: When custom host (api_endpoint) is set, `scopes` must *also* be set on the
        # credentials object
        super().__init__(
            host=host,
            credentials=credentials,
            client_info=client_info,
            always_use_jwt_access=always_use_jwt_access,
            url_scheme=url_scheme,
            api_audience=api_audience,
        )
        self._session = AuthorizedSession(
            self._credentials, default_host=self.DEFAULT_HOST
        )
        if client_cert_source_for_mtls:
            self._session.configure_mtls_channel(client_cert_source_for_mtls)
        self._interceptor = interceptor or PermissionServiceRestInterceptor()
        self._prep_wrapped_messages(client_info)

    class _CreatePermission(
        _BasePermissionServiceRestTransport._BaseCreatePermission,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.CreatePermission")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
                data=body,
            )
            return response

        def __call__(
            self,
            request: permission_service.CreatePermissionRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ) -> gag_permission.Permission:
            r"""Call the create permission method over HTTP.

            Args:
                request (~.permission_service.CreatePermissionRequest):
                    The request object. Request to create a ``Permission``.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.

            Returns:
                ~.gag_permission.Permission:
                    Permission resource grants user,
                group or the rest of the world access to
                the PaLM API resource (e.g. a tuned
                model, file).

                A role is a collection of permitted
                operations that allows users to perform
                specific actions on PaLM API resources.
                To make them available to users, groups,
                or service accounts, you assign roles.
                When you assign a role, you grant
                permissions that the role contains.

                There are three concentric roles. Each
                role is a superset of the previous
                role's permitted operations:

                 - reader can use the resource (e.g.
                  tuned model) for inference
                 - writer has reader's permissions and
                  additionally can edit and share
                 - owner has writer's permissions and
                  additionally can delete

            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseCreatePermission._get_http_options()
            )

            request, metadata = self._interceptor.pre_create_permission(
                request, metadata
            )
            transcoded_request = _BasePermissionServiceRestTransport._BaseCreatePermission._get_transcoded_request(
                http_options, request
            )

            body = _BasePermissionServiceRestTransport._BaseCreatePermission._get_request_body_json(
                transcoded_request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseCreatePermission._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = type(request).to_json(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.CreatePermission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "CreatePermission",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._CreatePermission._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
                body,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

            # Return the response
            resp = gag_permission.Permission()
            pb_resp = gag_permission.Permission.pb(resp)

            json_format.Parse(response.content, pb_resp, ignore_unknown_fields=True)

            resp = self._interceptor.post_create_permission(resp)
            response_metadata = [(k, str(v)) for k, v in response.headers.items()]
            resp, _ = self._interceptor.post_create_permission_with_metadata(
                resp, response_metadata
            )
            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                try:
                    response_payload = gag_permission.Permission.to_json(response)
                except:
                    response_payload = None
                http_response = {
                    "payload": response_payload,
                    "headers": dict(response.headers),
                    "status": response.status_code,
                }
                _LOGGER.debug(
                    "Received response for google.ai.generativelanguage_v1beta3.PermissionServiceClient.create_permission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "CreatePermission",
                        "metadata": http_response["headers"],
                        "httpResponse": http_response,
                    },
                )
            return resp

    class _DeletePermission(
        _BasePermissionServiceRestTransport._BaseDeletePermission,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.DeletePermission")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
            )
            return response

        def __call__(
            self,
            request: permission_service.DeletePermissionRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ):
            r"""Call the delete permission method over HTTP.

            Args:
                request (~.permission_service.DeletePermissionRequest):
                    The request object. Request to delete the ``Permission``.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.
            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseDeletePermission._get_http_options()
            )

            request, metadata = self._interceptor.pre_delete_permission(
                request, metadata
            )
            transcoded_request = _BasePermissionServiceRestTransport._BaseDeletePermission._get_transcoded_request(
                http_options, request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseDeletePermission._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = json_format.MessageToJson(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.DeletePermission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "DeletePermission",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._DeletePermission._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

    class _GetPermission(
        _BasePermissionServiceRestTransport._BaseGetPermission,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.GetPermission")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
            )
            return response

        def __call__(
            self,
            request: permission_service.GetPermissionRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ) -> permission.Permission:
            r"""Call the get permission method over HTTP.

            Args:
                request (~.permission_service.GetPermissionRequest):
                    The request object. Request for getting information about a specific
                ``Permission``.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.

            Returns:
                ~.permission.Permission:
                    Permission resource grants user,
                group or the rest of the world access to
                the PaLM API resource (e.g. a tuned
                model, file).

                A role is a collection of permitted
                operations that allows users to perform
                specific actions on PaLM API resources.
                To make them available to users, groups,
                or service accounts, you assign roles.
                When you assign a role, you grant
                permissions that the role contains.

                There are three concentric roles. Each
                role is a superset of the previous
                role's permitted operations:

                 - reader can use the resource (e.g.
                  tuned model) for inference
                 - writer has reader's permissions and
                  additionally can edit and share
                 - owner has writer's permissions and
                  additionally can delete

            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseGetPermission._get_http_options()
            )

            request, metadata = self._interceptor.pre_get_permission(request, metadata)
            transcoded_request = _BasePermissionServiceRestTransport._BaseGetPermission._get_transcoded_request(
                http_options, request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseGetPermission._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = type(request).to_json(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.GetPermission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "GetPermission",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._GetPermission._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

            # Return the response
            resp = permission.Permission()
            pb_resp = permission.Permission.pb(resp)

            json_format.Parse(response.content, pb_resp, ignore_unknown_fields=True)

            resp = self._interceptor.post_get_permission(resp)
            response_metadata = [(k, str(v)) for k, v in response.headers.items()]
            resp, _ = self._interceptor.post_get_permission_with_metadata(
                resp, response_metadata
            )
            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                try:
                    response_payload = permission.Permission.to_json(response)
                except:
                    response_payload = None
                http_response = {
                    "payload": response_payload,
                    "headers": dict(response.headers),
                    "status": response.status_code,
                }
                _LOGGER.debug(
                    "Received response for google.ai.generativelanguage_v1beta3.PermissionServiceClient.get_permission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "GetPermission",
                        "metadata": http_response["headers"],
                        "httpResponse": http_response,
                    },
                )
            return resp

    class _ListPermissions(
        _BasePermissionServiceRestTransport._BaseListPermissions,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.ListPermissions")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
            )
            return response

        def __call__(
            self,
            request: permission_service.ListPermissionsRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ) -> permission_service.ListPermissionsResponse:
            r"""Call the list permissions method over HTTP.

            Args:
                request (~.permission_service.ListPermissionsRequest):
                    The request object. Request for listing permissions.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.

            Returns:
                ~.permission_service.ListPermissionsResponse:
                    Response from ``ListPermissions`` containing a paginated
                list of permissions.

            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseListPermissions._get_http_options()
            )

            request, metadata = self._interceptor.pre_list_permissions(
                request, metadata
            )
            transcoded_request = _BasePermissionServiceRestTransport._BaseListPermissions._get_transcoded_request(
                http_options, request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseListPermissions._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = type(request).to_json(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.ListPermissions",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "ListPermissions",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._ListPermissions._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

            # Return the response
            resp = permission_service.ListPermissionsResponse()
            pb_resp = permission_service.ListPermissionsResponse.pb(resp)

            json_format.Parse(response.content, pb_resp, ignore_unknown_fields=True)

            resp = self._interceptor.post_list_permissions(resp)
            response_metadata = [(k, str(v)) for k, v in response.headers.items()]
            resp, _ = self._interceptor.post_list_permissions_with_metadata(
                resp, response_metadata
            )
            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                try:
                    response_payload = (
                        permission_service.ListPermissionsResponse.to_json(response)
                    )
                except:
                    response_payload = None
                http_response = {
                    "payload": response_payload,
                    "headers": dict(response.headers),
                    "status": response.status_code,
                }
                _LOGGER.debug(
                    "Received response for google.ai.generativelanguage_v1beta3.PermissionServiceClient.list_permissions",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "ListPermissions",
                        "metadata": http_response["headers"],
                        "httpResponse": http_response,
                    },
                )
            return resp

    class _TransferOwnership(
        _BasePermissionServiceRestTransport._BaseTransferOwnership,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.TransferOwnership")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
                data=body,
            )
            return response

        def __call__(
            self,
            request: permission_service.TransferOwnershipRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ) -> permission_service.TransferOwnershipResponse:
            r"""Call the transfer ownership method over HTTP.

            Args:
                request (~.permission_service.TransferOwnershipRequest):
                    The request object. Request to transfer the ownership of
                the tuned model.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.

            Returns:
                ~.permission_service.TransferOwnershipResponse:
                    Response from ``TransferOwnership``.
            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseTransferOwnership._get_http_options()
            )

            request, metadata = self._interceptor.pre_transfer_ownership(
                request, metadata
            )
            transcoded_request = _BasePermissionServiceRestTransport._BaseTransferOwnership._get_transcoded_request(
                http_options, request
            )

            body = _BasePermissionServiceRestTransport._BaseTransferOwnership._get_request_body_json(
                transcoded_request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseTransferOwnership._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = type(request).to_json(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.TransferOwnership",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "TransferOwnership",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._TransferOwnership._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
                body,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

            # Return the response
            resp = permission_service.TransferOwnershipResponse()
            pb_resp = permission_service.TransferOwnershipResponse.pb(resp)

            json_format.Parse(response.content, pb_resp, ignore_unknown_fields=True)

            resp = self._interceptor.post_transfer_ownership(resp)
            response_metadata = [(k, str(v)) for k, v in response.headers.items()]
            resp, _ = self._interceptor.post_transfer_ownership_with_metadata(
                resp, response_metadata
            )
            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                try:
                    response_payload = (
                        permission_service.TransferOwnershipResponse.to_json(response)
                    )
                except:
                    response_payload = None
                http_response = {
                    "payload": response_payload,
                    "headers": dict(response.headers),
                    "status": response.status_code,
                }
                _LOGGER.debug(
                    "Received response for google.ai.generativelanguage_v1beta3.PermissionServiceClient.transfer_ownership",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "TransferOwnership",
                        "metadata": http_response["headers"],
                        "httpResponse": http_response,
                    },
                )
            return resp

    class _UpdatePermission(
        _BasePermissionServiceRestTransport._BaseUpdatePermission,
        PermissionServiceRestStub,
    ):
        def __hash__(self):
            return hash("PermissionServiceRestTransport.UpdatePermission")

        @staticmethod
        def _get_response(
            host,
            metadata,
            query_params,
            session,
            timeout,
            transcoded_request,
            body=None,
        ):
            uri = transcoded_request["uri"]
            method = transcoded_request["method"]
            headers = dict(metadata)
            headers["Content-Type"] = "application/json"
            response = getattr(session, method)(
                "{host}{uri}".format(host=host, uri=uri),
                timeout=timeout,
                headers=headers,
                params=rest_helpers.flatten_query_params(query_params, strict=True),
                data=body,
            )
            return response

        def __call__(
            self,
            request: permission_service.UpdatePermissionRequest,
            *,
            retry: OptionalRetry = gapic_v1.method.DEFAULT,
            timeout: Optional[float] = None,
            metadata: Sequence[Tuple[str, Union[str, bytes]]] = (),
        ) -> gag_permission.Permission:
            r"""Call the update permission method over HTTP.

            Args:
                request (~.permission_service.UpdatePermissionRequest):
                    The request object. Request to update the ``Permission``.
                retry (google.api_core.retry.Retry): Designation of what errors, if any,
                    should be retried.
                timeout (float): The timeout for this request.
                metadata (Sequence[Tuple[str, Union[str, bytes]]]): Key/value pairs which should be
                    sent along with the request as metadata. Normally, each value must be of type `str`,
                    but for metadata keys ending with the suffix `-bin`, the corresponding values must
                    be of type `bytes`.

            Returns:
                ~.gag_permission.Permission:
                    Permission resource grants user,
                group or the rest of the world access to
                the PaLM API resource (e.g. a tuned
                model, file).

                A role is a collection of permitted
                operations that allows users to perform
                specific actions on PaLM API resources.
                To make them available to users, groups,
                or service accounts, you assign roles.
                When you assign a role, you grant
                permissions that the role contains.

                There are three concentric roles. Each
                role is a superset of the previous
                role's permitted operations:

                 - reader can use the resource (e.g.
                  tuned model) for inference
                 - writer has reader's permissions and
                  additionally can edit and share
                 - owner has writer's permissions and
                  additionally can delete

            """

            http_options = (
                _BasePermissionServiceRestTransport._BaseUpdatePermission._get_http_options()
            )

            request, metadata = self._interceptor.pre_update_permission(
                request, metadata
            )
            transcoded_request = _BasePermissionServiceRestTransport._BaseUpdatePermission._get_transcoded_request(
                http_options, request
            )

            body = _BasePermissionServiceRestTransport._BaseUpdatePermission._get_request_body_json(
                transcoded_request
            )

            # Jsonify the query params
            query_params = _BasePermissionServiceRestTransport._BaseUpdatePermission._get_query_params_json(
                transcoded_request
            )

            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                request_url = "{host}{uri}".format(
                    host=self._host, uri=transcoded_request["uri"]
                )
                method = transcoded_request["method"]
                try:
                    request_payload = type(request).to_json(request)
                except:
                    request_payload = None
                http_request = {
                    "payload": request_payload,
                    "requestMethod": method,
                    "requestUrl": request_url,
                    "headers": dict(metadata),
                }
                _LOGGER.debug(
                    f"Sending request for google.ai.generativelanguage_v1beta3.PermissionServiceClient.UpdatePermission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "UpdatePermission",
                        "httpRequest": http_request,
                        "metadata": http_request["headers"],
                    },
                )

            # Send the request
            response = PermissionServiceRestTransport._UpdatePermission._get_response(
                self._host,
                metadata,
                query_params,
                self._session,
                timeout,
                transcoded_request,
                body,
            )

            # In case of error, raise the appropriate core_exceptions.GoogleAPICallError exception
            # subclass.
            if response.status_code >= 400:
                raise core_exceptions.from_http_response(response)

            # Return the response
            resp = gag_permission.Permission()
            pb_resp = gag_permission.Permission.pb(resp)

            json_format.Parse(response.content, pb_resp, ignore_unknown_fields=True)

            resp = self._interceptor.post_update_permission(resp)
            response_metadata = [(k, str(v)) for k, v in response.headers.items()]
            resp, _ = self._interceptor.post_update_permission_with_metadata(
                resp, response_metadata
            )
            if CLIENT_LOGGING_SUPPORTED and _LOGGER.isEnabledFor(
                logging.DEBUG
            ):  # pragma: NO COVER
                try:
                    response_payload = gag_permission.Permission.to_json(response)
                except:
                    response_payload = None
                http_response = {
                    "payload": response_payload,
                    "headers": dict(response.headers),
                    "status": response.status_code,
                }
                _LOGGER.debug(
                    "Received response for google.ai.generativelanguage_v1beta3.PermissionServiceClient.update_permission",
                    extra={
                        "serviceName": "google.ai.generativelanguage.v1beta3.PermissionService",
                        "rpcName": "UpdatePermission",
                        "metadata": http_response["headers"],
                        "httpResponse": http_response,
                    },
                )
            return resp

    @property
    def create_permission(
        self,
    ) -> Callable[
        [permission_service.CreatePermissionRequest], gag_permission.Permission
    ]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._CreatePermission(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def delete_permission(
        self,
    ) -> Callable[[permission_service.DeletePermissionRequest], empty_pb2.Empty]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._DeletePermission(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def get_permission(
        self,
    ) -> Callable[[permission_service.GetPermissionRequest], permission.Permission]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._GetPermission(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def list_permissions(
        self,
    ) -> Callable[
        [permission_service.ListPermissionsRequest],
        permission_service.ListPermissionsResponse,
    ]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._ListPermissions(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def transfer_ownership(
        self,
    ) -> Callable[
        [permission_service.TransferOwnershipRequest],
        permission_service.TransferOwnershipResponse,
    ]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._TransferOwnership(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def update_permission(
        self,
    ) -> Callable[
        [permission_service.UpdatePermissionRequest], gag_permission.Permission
    ]:
        # The return type is fine, but mypy isn't sophisticated enough to determine what's going on here.
        # In C++ this would require a dynamic_cast
        return self._UpdatePermission(self._session, self._host, self._interceptor)  # type: ignore

    @property
    def kind(self) -> str:
        return "rest"

    def close(self):
        self._session.close()


__all__ = ("PermissionServiceRestTransport",)
