# AI Research Agent

A powerful AI research assistant that uses **Ollama with Llama 3.2** to conduct comprehensive research using web search and Wikipedia. This agent runs completely locally on your machine, ensuring privacy and eliminating API costs.

## 🚀 Features

- 🤖 **Local AI**: Uses Ollama with Llama 3.2 (completely free, no API keys needed)
- 🌐 **Web Search**: DuckDuckGo integration for current information
- 📚 **Wikipedia**: Access to encyclopedic knowledge
- 💾 **Save Results**: Export research to text files with timestamps
- 🔒 **Privacy**: Everything runs locally on your machine
- ⚡ **Fast**: No network latency for AI processing
- 🆓 **Free**: No subscription fees or usage limits

## 📋 Prerequisites

Before setting up the AI Research Agent, ensure you have:

1. **Python 3.12+** installed on your system
2. **Ollama** installed and running
3. **Llama 3.2 model** downloaded in Ollama
4. **PowerShell** (for Windows users)

### Installing Ollama and Llama 3.2

1. **Install Ollama**:
   - Visit [ollama.ai](https://ollama.ai/) and download for your OS
   - Follow the installation instructions

2. **Download Llama 3.2**:
   ```bash
   ollama pull llama3.2
   ```

3. **Verify installation**:
   ```bash
   ollama list
   ```
   You should see `llama3.2:latest` in the list.

## 🛠️ Setup Instructions

### 1. Clone or Download the Project
```bash
git clone <repository-url>
cd "AI Research Agent"
```

### 2. Activate Virtual Environment
The project comes with a pre-configured virtual environment. Activate it:

**Windows (PowerShell):**
```powershell
.\RAgent_venv\Scripts\Activate.ps1
```

**Windows (Command Prompt):**
```cmd
.\RAgent_venv\Scripts\activate.bat
```

You should see `(RAgent_venv)` prefix in your terminal, indicating the virtual environment is active.

### 3. Verify Dependencies (Optional)
All dependencies are already installed in the virtual environment. To verify:
```bash
pip list
```

### 4. Test Ollama Connection
Ensure Ollama is running and accessible:
```bash
ollama list
```

## 🎯 How to Use

### Option 1: Web Interface (Recommended)

**Quick Start (Easy Method):**
- **Windows**: Double-click `start_web.bat` or `start_web.ps1`
- The web interface will automatically open at `http://localhost:5000`

**Manual Method:**
1. **Activate Virtual Environment**:
   ```powershell
   .\RAgent_venv\Scripts\Activate.ps1
   ```

2. **Start the Web Server**:
   ```powershell
   .\RAgent_venv\Scripts\python.exe app.py
   ```

3. **Open Your Browser** and go to:
   ```
   http://localhost:5000
   ```

**Using the Web Interface:**
- 🖥️ **Modern UI**: Beautiful, responsive web interface
- 📝 **Easy Input**: Large text area for research queries
- ⚡ **Real-time Progress**: Live updates with progress bars and timestamps
- 📊 **Detailed Progress**: See exactly what the AI is doing at each step
- 📈 **Rich Results**: Comprehensive reports with statistics
- 💾 **One-click Save**: Download reports instantly
- 📱 **Mobile Friendly**: Works on phones and tablets
- 🔄 **WebSocket Updates**: Instant progress notifications

### Option 2: Command Line Interface

1. **Activate Virtual Environment**:
   ```powershell
   .\RAgent_venv\Scripts\Activate.ps1
   ```

2. **Run the CLI Research Agent**:
   ```powershell
   .\RAgent_venv\Scripts\python.exe research_agent.py
   ```

3. **Enter Your Research Query** when prompted:
   ```
   🤖 AI Research Agent with Llama 3.2
   Available tools: Web Search, Wikipedia, Save to File
   --------------------------------------------------
   What can I help you research?
   ```

### Example Usage Session

```
What can I help you research? artificial intelligence in healthcare

🔍 Researching: artificial intelligence in healthcare
--------------------------------------------------
🌐 Searching the web...
Web search completed. Found information.
📚 Searching Wikipedia...
Wikipedia search completed.
🧠 Generating research summary...

============================================================
📋 RESEARCH RESULTS
============================================================
[Comprehensive research summary will be displayed here]
============================================================

💾 Would you like to save this research to a file? (y/n): y
Enter filename (or press Enter for default): ai_healthcare_research.txt
✅ Research saved to ai_healthcare_research.txt

🎉 Research completed successfully!
```

## 📝 Example Research Queries

Try these example queries to test the agent:

- `"Tell me about artificial intelligence"`
- `"Research renewable energy technologies"`
- `"What is quantum computing?"`
- `"Explain machine learning algorithms"`
- `"Latest developments in space exploration"`
- `"Climate change impact and solutions"`
- `"Blockchain technology applications"`

## 📁 Project Structure

```
AI Research Agent/
├── RAgent_venv/              # Virtual environment
│   ├── Scripts/              # Python executables
│   ├── Lib/                  # Installed packages
│   └── pyvenv.cfg           # Environment configuration
├── templates/                # Web interface templates
│   └── index.html           # Main web interface
├── static/                   # Static web assets (CSS, JS, images)
├── app.py                   # Flask web application
├── research_agent.py         # CLI research agent script
├── tools.py                 # Research tools (search, wiki, save)
├── requirements.txt         # Project dependencies
└── README.md               # This documentation
```

## 🔧 Dependencies

The project uses the following Python packages (pre-installed in virtual environment):

**Core AI & Research:**
- **langchain** - LLM framework for building AI applications
- **langchain-ollama** - Ollama integration for LangChain
- **langchain-community** - Community tools and integrations
- **wikipedia** - Wikipedia API for encyclopedic information
- **duckduckgo-search** - Web search functionality
- **pydantic** - Data validation and parsing

**Web Interface:**
- **flask** - Web framework for the user interface
- **flask-cors** - Cross-origin resource sharing support

## 🐛 Troubleshooting

### Common Issues and Solutions

1. **"Ollama not found" error**:
   - Ensure Ollama is installed and running
   - Check if `ollama list` works in terminal

2. **"llama3.2 model not found"**:
   - Run `ollama pull llama3.2` to download the model

3. **Virtual environment activation fails**:
   - Ensure you're in the correct directory
   - Try using Command Prompt instead of PowerShell
   - Check execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

4. **Import errors**:
   - Ensure virtual environment is activated (look for `(RAgent_venv)` prefix)
   - Use full path: `.\RAgent_venv\Scripts\python.exe research_agent.py`

5. **Slow responses**:
   - This is normal for local AI models
   - Ensure your system has sufficient RAM (8GB+ recommended)

## 💡 Tips for Better Results

1. **Be Specific**: More specific queries yield better research results
2. **Use Keywords**: Include relevant keywords in your research queries
3. **Save Important Research**: Use the save feature for research you want to reference later
4. **Multiple Queries**: Break complex topics into multiple focused queries

## 🔒 Privacy & Security

- **Complete Privacy**: All processing happens locally on your machine
- **No Data Sharing**: No information is sent to external AI services
- **Offline Capable**: Works without internet for AI processing (internet needed only for web search)
- **No API Keys**: No need to sign up for external AI services

## 📄 License

This project is open source and available under the MIT License.

## 👨‍💻 Credits

**Created by Chaitanya with Augment under Open Network Solutions**

---

*Happy Researching! 🚀*