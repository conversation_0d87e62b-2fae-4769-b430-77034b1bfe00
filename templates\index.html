<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Research Agent</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .research-form {
            padding: 40px;
        }
        
        .query-input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .query-input:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .research-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .research-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .progress-container {
            margin-top: 30px;
            display: none;
        }
        
        .progress-step {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 4px solid #4facfe;
            animation: slideIn 0.3s ease-out;
        }

        .progress-step.info {
            border-left-color: #4facfe;
            background: #f8f9fa;
        }

        .progress-step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        #progressSteps {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .results-container {
            margin-top: 30px;
            display: none;
        }
        
        .result-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #4facfe;
        }
        
        .result-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        
        .save-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #4facfe;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #e9ecef;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-robot"></i> AI Research Agent</h1>
                <p class="mb-0">Powered by Llama 3.2 via Ollama - Advanced Multi-Stage Analysis</p>
            </div>
            
            <!-- Main Content -->
            <div class="research-form">
                <!-- Features Section -->
                <div class="row mb-4" id="features-section">
                    <div class="col-md-3">
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-brain"></i></div>
                            <h5>Local AI</h5>
                            <p>Powered by Llama 3.2 running locally</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-search"></i></div>
                            <h5>Web Search</h5>
                            <p>Real-time information from the web</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fab fa-wikipedia-w"></i></div>
                            <h5>Wikipedia</h5>
                            <p>Encyclopedic knowledge base</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="feature-card">
                            <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                            <h5>Deep Analysis</h5>
                            <p>Multi-stage analytical insights</p>
                        </div>
                    </div>
                </div>
                
                <!-- Research Form -->
                <div class="row">
                    <div class="col-12">
                        <div class="mb-4">
                            <label for="queryInput" class="form-label h5">
                                <i class="fas fa-question-circle"></i> What would you like to research?
                            </label>
                            <textarea 
                                class="form-control query-input" 
                                id="queryInput" 
                                rows="3" 
                                placeholder="Enter your research topic here... (e.g., 'artificial intelligence in healthcare', 'renewable energy technologies', 'quantum computing')"
                            ></textarea>
                        </div>
                        <div class="text-center">
                            <button class="btn research-btn" id="researchBtn" onclick="startResearch()">
                                <i class="fas fa-search"></i> Start Research Analysis
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Section -->
                <div class="progress-container" id="progressContainer">
                    <h4><i class="fas fa-cogs"></i> Research Progress</h4>
                    <div id="progressSteps"></div>
                </div>
                
                <!-- Results Section -->
                <div class="results-container" id="resultsContainer">
                    <div class="card shadow-lg">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h3 class="mb-0"><i class="fas fa-chart-line"></i> Research Results</h3>
                            <div>
                                <button class="btn btn-light btn-sm me-2" onclick="downloadReport()">
                                    <i class="fas fa-download"></i> Download Report
                                </button>
                                <button class="btn btn-outline-light btn-sm" onclick="startNewResearch()">
                                    <i class="fas fa-plus"></i> New Research
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Research Info -->
                            <div class="alert alert-info mb-4">
                                <div class="row">
                                    <div class="col-md-8">
                                        <strong>Query:</strong> <span id="researchQuery"></span><br>
                                        <strong>Generated:</strong> <span id="researchTimestamp"></span>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number" id="webWordsCount">0</div>
                                        <div class="stat-label">Web Words</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number" id="wikiWordsCount">0</div>
                                        <div class="stat-label">Wiki Words</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number" id="totalWordsCount">0</div>
                                        <div class="stat-label">Total Words</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number" id="analysisTime">0s</div>
                                        <div class="stat-label">Analysis Time</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Final Report -->
                            <div class="report-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4><i class="fas fa-file-alt"></i> Comprehensive Research Report</h4>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm" onclick="copyReport()">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="downloadReport()">
                                            <i class="fas fa-download"></i> Download
                                        </button>
                                    </div>
                                </div>
                                <div id="finalReport" class="report-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <p><strong>Created by Chaitanya with Augment under Open Network Solutions</strong></p>
                <p><i class="fas fa-shield-alt"></i> Privacy-focused • <i class="fas fa-microchip"></i> Local AI Processing • <i class="fas fa-free-code-camp"></i> Completely Free</p>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script>
        let currentResearchId = null;
        let startTime = null;
        let socket = null;

        // Initialize Socket.IO connection
        function initializeSocket() {
            socket = io();

            socket.on('connect', function() {
                console.log('Connected to server');
            });

            socket.on('disconnect', function() {
                console.log('Disconnected from server');
            });

            socket.on('research_started', function(data) {
                console.log('Research started:', data);
                currentResearchId = data.research_id;
                clearProgressSteps();
                addProgressStep('🚀 Research started for: ' + data.query, 'info');
            });

            socket.on('progress_update', function(data) {
                console.log('Progress update:', data);
                addProgressStep(data.message, 'info', data.progress);
            });

            socket.on('research_completed', function(data) {
                console.log('Research completed:', data);
                displayResults(data.results);
            });

            socket.on('research_error', function(data) {
                console.log('Research error:', data);
                addProgressStep('❌ Error: ' + data.error, 'error');
                resetResearchButton();
            });
        }

        function clearProgressSteps() {
            document.getElementById('progressSteps').innerHTML = '';
        }

        function addProgressStep(message, type = 'info', progress = null) {
            const progressSteps = document.getElementById('progressSteps');
            const stepDiv = document.createElement('div');
            stepDiv.className = `progress-step ${type}`;

            let progressBar = '';
            if (progress !== null) {
                progressBar = `
                    <div class="progress mt-2" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: ${progress}%"
                             aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                `;
            }

            stepDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${message}</span>
                    <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                </div>
                ${progressBar}
            `;

            progressSteps.appendChild(stepDiv);
            progressSteps.scrollTop = progressSteps.scrollHeight;
        }

        function resetResearchButton() {
            const btn = document.getElementById('researchBtn');
            btn.innerHTML = '<i class="fas fa-search"></i> Start Research Analysis';
            btn.disabled = false;
        }

        function startResearch() {
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                alert('Please enter a research topic!');
                return;
            }

            // Initialize socket if not already done
            if (!socket) {
                initializeSocket();
            }

            // Hide features and show progress
            document.getElementById('features-section').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';

            // Update button state
            const btn = document.getElementById('researchBtn');
            btn.innerHTML = '<span class="loading-spinner"></span> Researching...';
            btn.disabled = true;

            startTime = Date.now();

            // Start research via WebSocket
            socket.emit('start_research', { query: query });
        }

        function displayResults(results) {
            // Store current research ID globally
            currentResearchId = results.research_id;

            // Update research info
            document.getElementById('researchQuery').textContent = results.query;
            document.getElementById('researchTimestamp').textContent = results.timestamp;

            // Update statistics
            document.getElementById('webWordsCount').textContent = results.word_counts.web;
            document.getElementById('wikiWordsCount').textContent = results.word_counts.wiki;
            document.getElementById('totalWordsCount').textContent = results.word_counts.total;
            document.getElementById('analysisTime').textContent = results.analysis_time + 's';

            // Display final report
            document.getElementById('finalReport').innerHTML = formatReport(results.final_report);

            // Reset button
            resetResearchButton();

            // Show results
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
        }

        function formatReport(report) {
            // Convert markdown-style headers to HTML
            return report
                .replace(/## (.*)/g, '<h4 class="text-primary mt-4 mb-3">$1</h4>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/^/, '<p>')
                .replace(/$/, '</p>');
        }

        function downloadReport() {
            if (!currentResearchId) {
                alert('No research data to download!');
                return;
            }

            // Download via API endpoint
            window.open(`/api/download/${currentResearchId}`, '_blank');
        }

        function copyReport() {
            if (!currentResearchId) {
                alert('No research data to copy!');
                return;
            }

            const report = document.getElementById('finalReport').innerText;
            navigator.clipboard.writeText(report).then(function() {
                // Show temporary success message
                const btn = event.target;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-outline-primary');

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-primary');
                }, 2000);
            }).catch(function(err) {
                alert('Failed to copy to clipboard');
            });
        }

        function startNewResearch() {
            // Reset the interface for new research
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('features-section').style.display = 'block';
            document.getElementById('queryInput').value = '';
            document.getElementById('queryInput').focus();
            currentResearchId = null;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize socket connection
            initializeSocket();

            // Allow Enter key to start research
            document.getElementById('queryInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    startResearch();
                }
            });
        });
    </script>
</body>
</html>
