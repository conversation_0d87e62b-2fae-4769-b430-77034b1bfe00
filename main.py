from langchain_ollama import OllamaLLM
from langchain_core.prompts import PromptTemplate
from langchain.agents import create_react_agent, AgentExecutor
from tools import search_tool, wiki_tool, save_tool

# Create a simple ReAct prompt template
react_prompt = PromptTemplate.from_template("""
Answer the following questions as best you can. You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}
""")

llm = OllamaLLM(model="llama3.2")
tools = [search_tool, wiki_tool, save_tool]

agent = create_react_agent(
    llm=llm,
    tools=tools,
    prompt=react_prompt
)

agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True, max_iterations=5)

print("🤖 AI Research Agent with Llama 3.2 is ready!")
print("Available tools: Web Search, Wikipedia, Save to File")
print("-" * 50)

query = input("What can I help you research? ")
print(f"\n🔍 Researching: {query}")
print("-" * 50)

try:
    raw_response = agent_executor.invoke({"input": query})

    print("\n" + "="*50)
    print("📋 RESEARCH RESULTS:")
    print("="*50)
    print(raw_response["output"])
    print("="*50)

except Exception as e:
    print(f"❌ Error occurred: {e}")
    print("Please try again with a different query.")