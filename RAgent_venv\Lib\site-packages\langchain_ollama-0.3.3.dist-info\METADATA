Metadata-Version: 2.1
Name: langchain-ollama
Version: 0.3.3
Summary: An integration package connecting Ollama and LangChain
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/partners/ollama
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-ollama%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: ollama<1.0.0,>=0.4.8
Requires-Dist: langchain-core<1.0.0,>=0.3.60
Description-Content-Type: text/markdown

# langchain-ollama

This package contains the LangChain integration with Ollama

## Installation

```bash
pip install -U langchain-ollama
```

You will also need to run the Ollama server locally. 
You can download it [here](https://ollama.com/download).

## Chat Models

`ChatOllama` class exposes chat models from Ollama.

```python
from langchain_ollama import ChatOllama

llm = ChatOllama(model="llama3-groq-tool-use")
llm.invoke("Sing a ballad of LangChain.")
```

## Embeddings

`OllamaEmbeddings` class exposes embeddings from Ollama.

```python
from langchain_ollama import OllamaEmbeddings

embeddings = OllamaEmbeddings(model="llama3")
embeddings.embed_query("What is the meaning of life?")
```

## LLMs
`OllamaLLM` class exposes LLMs from Ollama.

```python
from langchain_ollama import OllamaLLM

llm = OllamaLLM(model="llama3")
llm.invoke("The meaning of life is")
```
